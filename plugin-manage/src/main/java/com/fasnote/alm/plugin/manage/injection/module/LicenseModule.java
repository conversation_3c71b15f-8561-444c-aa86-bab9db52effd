package com.fasnote.alm.plugin.manage.injection.module;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceInterceptor;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.LicenseValidationEnhancedInterceptor;
import com.fasnote.alm.plugin.manage.injection.interceptor.LicenseAwareServiceInterceptor;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.security.ISecurityValidator;
import com.fasnote.alm.plugin.manage.security.SecurityValidationInterceptor;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证模块 通过DI框架的扩展机制提供许可证相关功能 负责从许可证文件中解密和注册业务服务实现
 */
public class LicenseModule implements IModule {

	/**
	 * 许可证感知服务提供者
	 */
	private class LicenseAwareProvider implements IServiceProvider<LicenseAware> {

		@Override
		public LicenseAware provide(IInjectionContext context) {
			// 返回一个简单的许可证信息访问器
			return new LicenseAware() {
				private PluginLicense license;

				@Override
				public PluginLicense getLicenseInfo() {
					return license;
				}

				@Override
				public void setLicenseInfo(PluginLicense license) {
					this.license = license;
				}
			};
		}
	}

	/**
	 * 许可证服务拦截器 创建动态代理，在运行时根据许可证状态选择实现
	 */
	private class LicenseServiceInterceptor implements IServiceInterceptor {

		@Override
		public Object afterCreate(Class<?> serviceClass, Object instance, IInjectionContext context) {
			// 如果实例实现了LicenseAware接口，注入许可证信息
			if (instance instanceof LicenseAware) {
				Class<?> serviceInterface = findLicenseableInterface(serviceClass);
				if (serviceInterface != null) {
					injectLicenseInfo((LicenseAware) instance, serviceInterface);
				}
			}
			return instance;
		}

		@Override
		public Object beforeCreate(Class<?> serviceClass, IInjectionContext context) {
			// 主要逻辑已移到服务注册阶段，这里只做简单检查
			return null;
		}

		/**
		 * 找到实现类对应的可许可接口
		 */
		private Class<?> findLicenseableInterface(Class<?> clazz) {
			// 如果本身就是接口，直接返回
			if (clazz.isInterface()) {
				return clazz;
			}

			// 查找实现的接口
			Class<?>[] interfaces = clazz.getInterfaces();
			for (Class<?> intf : interfaces) {
				String packageName = intf.getPackage().getName();
				if (!packageName.startsWith("java.") && !packageName.startsWith("javax.")
						&& !packageName.startsWith("org.osgi.") && !packageName.startsWith("org.slf4j.")) {
					return intf;
				}
			}
			return null;
		}

		@Override
		public int getPriority() {
			return 5; // 高优先级
		}

		@Override
		public boolean shouldIntercept(Class<?> serviceClass) {
			// 排除系统包
			if (serviceClass.getPackage() != null) {
				String packageName = serviceClass.getPackage().getName();
				if (packageName.startsWith("java.") || packageName.startsWith("javax.")
						|| packageName.startsWith("org.osgi.") || packageName.startsWith("org.slf4j.")) {
					return false;
				}
			}

			// 检查是否有对应的许可证接口
			Class<?> licenseableInterface = findLicenseableInterface(serviceClass);
			boolean shouldIntercept = licenseableInterface != null;

			logger.debug("许可证拦截器检查: {} -> {} (接口: {})", serviceClass.getName(), shouldIntercept,
					licenseableInterface != null ? licenseableInterface.getName() : "无");
			return shouldIntercept;
		}
	}
	private static final Logger logger = LoggerFactory.getLogger(LicenseModule.class);
	private final LicenseManager licenseManager;

	/**
	 * 依赖注入构造函数
	 *
	 * @param licenseManager 许可证管理器（由DI框架注入）
	 */
	public LicenseModule(LicenseManager licenseManager) {
		if (licenseManager == null) {
			throw new IllegalArgumentException("LicenseManager不能为null");
		}
		this.licenseManager = licenseManager;
		logger.info("LicenseModule通过依赖注入创建");
	}

	@Override
	public void configure(IBinder binder) {
		logger.info("配置LicenseModule...");

		// 设置包扫描管理器的变更监听器
		//packageScanManager.setChangeListener(this);

		// 注解驱动的业务实现注册
		//registerBusinessImplementations(binder);

		// 动态注册许可证服务实现
		//registerLicenseServices(binder);

		// 注册许可证感知服务拦截器（新架构）
		LicenseAwareServiceInterceptor licenseAwareInterceptor = new LicenseAwareServiceInterceptor(licenseManager);
		binder.registerInterceptor(licenseAwareInterceptor);
		logger.info("许可证感知服务拦截器已注册，优先级: " + licenseAwareInterceptor.getPriority());

		// 注册许可证服务拦截器（保持向后兼容）
		LicenseServiceInterceptor licenseInterceptor = new LicenseServiceInterceptor();
		binder.registerInterceptor(licenseInterceptor);
		logger.info("许可证服务拦截器已注册，优先级: " + licenseInterceptor.getPriority());

		// 注册安全验证拦截器
		SecurityValidationInterceptor securityInterceptor = new SecurityValidationInterceptor();
		// 尝试获取SecurityValidator实例并设置到拦截器中
		try {
			ISecurityValidator securityValidator = getSecurityValidator();
			if (securityValidator != null) {
				securityInterceptor.setSecurityValidator(securityValidator);
				logger.debug("SecurityValidator已设置到安全验证拦截器");
			} else {
				logger.warn("SecurityValidator未找到，安全验证拦截器将无法正常工作");
			}
		} catch (Exception e) {
			logger.warn("获取SecurityValidator失败", e);
		}
		binder.registerInterceptor(securityInterceptor);

		// 注册增强的许可证验证拦截器（支持方法级别AOP）
		try {
			SecurityValidator concreteSecurityValidator = getConcreteSecurityValidator();
			if (concreteSecurityValidator != null) {
				LicenseValidationEnhancedInterceptor enhancedInterceptor = new LicenseValidationEnhancedInterceptor(
						licenseManager, concreteSecurityValidator);
				binder.registerInterceptor(enhancedInterceptor);
				logger.info("增强许可证验证拦截器已注册（支持方法级别AOP）");
			} else {
				logger.warn("无法创建增强许可证验证拦截器：SecurityValidator未找到");
			}
		} catch (Exception e) {
			logger.warn("注册增强许可证验证拦截器失败", e);
		}

		// 注册许可证感知服务提供者
		binder.bind(LicenseAware.class).toProvider(new LicenseAwareProvider()).build();

		logger.info("LicenseModule配置完成");
	}



	/**
	 * 获取具体的SecurityValidator实例 用于创建增强拦截器
	 */
	private SecurityValidator getConcreteSecurityValidator() {
		try {
			// 首先尝试从许可证中获取
			ISecurityValidator interfaceValidator = getSecurityValidator();
			if (interfaceValidator instanceof SecurityValidator) {
				return (SecurityValidator) interfaceValidator;
			}

			// 如果许可证中没有，创建默认的SecurityValidator实例
			SecurityValidator defaultValidator = new SecurityValidator();
			// 这里可能需要设置一些默认配置
			return defaultValidator;

		} catch (Exception e) {
			logger.debug("获取具体SecurityValidator失败", e);
			return null;
		}
	}

	@Override
	public String getName() {
		return "LicenseModule";
	}

	@Override
	public int getPriority() {
		return 10; // 高优先级，确保许可证检查在其他模块之前
	}

	/**
	 * 获取SecurityValidator实例 尝试从许可证中创建SecurityValidator实例
	 */
	private ISecurityValidator getSecurityValidator() {
		try {
			// 尝试从许可证中创建SecurityValidator
			Object securityValidator = licenseManager.createServiceInstanceFromLicense(ISecurityValidator.class);
			if (securityValidator instanceof ISecurityValidator) {
				ISecurityValidator validator = (ISecurityValidator) securityValidator;
				// 初始化验证器
				validator.initialize();
				return validator;
			}
		} catch (Exception e) {
			logger.debug("从许可证创建SecurityValidator失败", e);
		}

		return null;
	}

	/**
	 * 为LicenseAware实例注入许可证信息
	 */
	private void injectLicenseInfo(LicenseAware instance, Class<?> serviceClass) {
		try {
			// 尝试找到相关的许可证
			for (String pluginId : licenseManager.getRegisteredPluginIds()) {
				Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
				if (licenseOpt.isPresent()) {
					PluginLicense license = licenseOpt.get();
					if (license.isValid()) {
						// 检查是否包含此服务的映射
						if (license.hasServiceMappings()
								&& license.getServiceMappings().containsKey(serviceClass.getName())) {
							instance.setLicenseInfo(license);
							break;
						}
					}
				}
			}
		} catch (Exception e) {
			logger.warn("注入许可证信息失败: {}", serviceClass.getName(), e);
		}
	}





	/**
	 * 基于接口名称提供服务 许可证验证系统的核心方法，支持动态服务查找
	 *
	 * @param interfaceName 接口全限定名称
	 * @param context       注入上下文
	 * @return 服务实例，如果未找到返回null
	 */
	public Object provideServiceByInterfaceName(String interfaceName, IInjectionContext context) {
		if (licenseManager == null) {
			logger.debug("LicenseManager未初始化");
			return null;
		}

		logger.debug("基于接口名称查找许可证服务: {}", interfaceName);

		try {
			// 尝试通过接口名称加载接口类
			Class<?> interfaceClass = Class.forName(interfaceName);

			// 使用LicenseManager创建服务实例
			Object instance = licenseManager.createServiceInstanceFromLicense(interfaceClass);
			if (instance != null) {
				logger.info("成功通过接口名称创建服务实例: {}", interfaceName);
				return instance;
			}
		} catch (ClassNotFoundException e) {
			logger.warn("无法加载接口类: {}", interfaceName, e);
		} catch (Exception e) {
			logger.error("通过接口名称创建服务实例失败: {}", interfaceName, e);
		}

		logger.debug("未找到匹配的许可证服务: {}", interfaceName);
		return null;
	}





	// ==================== PackageScanProviderChangeListener 实现
	// ====================







}