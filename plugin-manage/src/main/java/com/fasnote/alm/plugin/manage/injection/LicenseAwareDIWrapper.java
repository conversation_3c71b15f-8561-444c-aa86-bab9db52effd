package com.fasnote.alm.plugin.manage.injection;

import java.lang.annotation.Annotation;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.processor.LicenseAnnotationProcessor;

/**
 * 许可证感知的DI包装类
 * 
 * 封装DI容器的getService方法，实现基于许可证状态和注解优先级的服务实现选择逻辑。
 * 
 * 优先级规则：
 * 1. @LicenseImplementation - 许可证实现，优先级最高
 * 2. @FallbackImplementation - 回退实现，根据priority属性排序
 * 3. 其他实现 - 默认优先级
 */
public class LicenseAwareDIWrapper {
    
    private static final Logger logger = LoggerFactory.getLogger(LicenseAwareDIWrapper.class);
    
    private final IDependencyInjector delegateInjector;
    private final LicenseManager licenseManager;

    /**
     * 构造函数
     */
    public LicenseAwareDIWrapper() {
        this.delegateInjector = DI.getInjector();
        this.licenseManager = getLicenseManager();
    }

    /**
     * 构造函数（用于测试）
     */
    public LicenseAwareDIWrapper(LicenseManager licenseManager) {
        this.delegateInjector = DI.getInjector();
        this.licenseManager = licenseManager;
    }

    /**
     * 获取LicenseManager实例
     */
    private LicenseManager getLicenseManager() {
        try {
            // 尝试从DI容器获取LicenseManager
            return delegateInjector.getService(LicenseManager.class);
        } catch (Exception e) {
            logger.warn("无法获取LicenseManager实例，许可证验证功能将不可用: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取服务实例（许可证感知）
     *
     * @param serviceClass 服务接口类
     * @return 根据许可证状态和优先级选择的服务实例
     */
    public <T> T getService(Class<T> serviceClass) {
        // 获取所有实现
        List<Class<?>> implementations = delegateInjector.getServiceImplementations(serviceClass);

        if (implementations.isEmpty()) {
            // 没有实现，使用默认DI逻辑
            return delegateInjector.getService(serviceClass);
        }

        if (implementations.size() == 1) {
            // 只有一个实现，直接使用
            return delegateInjector.getService(serviceClass);
        }

        // 多个实现，选择最佳实现
        Class<?> bestImplementation = selectBestImplementation(serviceClass, implementations);

        if (bestImplementation != null) {
            logger.debug("许可证感知服务选择: {} -> {}", serviceClass.getName(), bestImplementation.getName());
            // 直接创建选择的实现实例
            return createServiceInstance(serviceClass, bestImplementation);
        }

        // 降级到默认DI逻辑
        return delegateInjector.getService(serviceClass);
    }
    
    /**
     * 选择最佳服务实现（集成许可证验证）
     */
    private Class<?> selectBestImplementation(Class<?> serviceInterface, List<Class<?>> implementations) {
        logger.debug("为服务接口 {} 选择实现，候选数量: {}", serviceInterface.getName(), implementations.size());

        // 首先尝试许可证实现（如果许可证管理器可用）
        if (licenseManager != null) {
            for (Class<?> candidate : implementations) {
                if (isLicenseImplementation(candidate)) {
                    // 检查许可证是否有效
                    if (isLicenseValid(serviceInterface, candidate)) {
                        logger.info("选择许可证实现: {} -> {} (许可证验证通过)",
                                   serviceInterface.getName(), candidate.getName());
                        return candidate;
                    } else {
                        logger.debug("许可证实现验证失败: {}", candidate.getName());
                    }
                }
            }
        }

        // 如果没有有效的许可证实现，按优先级选择回退实现
        Class<?> bestCandidate = null;
        int bestPriority = Integer.MAX_VALUE;

        for (Class<?> candidate : implementations) {
            ServiceImplementationInfo info = extractImplementationInfo(candidate);

            logger.debug("候选实现: {} - {}", candidate.getName(), info);

            // 跳过许可证实现（已经在上面处理过）
            if (info.isPremium) {
                continue;
            }

            // 比较优先级
            if (info.priority < bestPriority) {
                bestCandidate = candidate;
                bestPriority = info.priority;
            }
        }

        if (bestCandidate != null) {
            logger.info("选择回退实现: {} -> {} (优先级: {})",
                       serviceInterface.getName(), bestCandidate.getName(), bestPriority);
        }

        return bestCandidate;
    }
    
    /**
     * 创建服务实例
     */
    @SuppressWarnings("unchecked")
    private <T> T createServiceInstance(Class<T> serviceClass, Class<?> implementationClass) {
        try {
            // 使用DI容器创建实例（支持依赖注入）
            return (T) delegateInjector.getService(implementationClass);
        } catch (Exception e) {
            logger.warn("使用DI容器创建实例失败: {}, 尝试直接实例化", implementationClass.getName(), e);
            try {
                // 降级到直接实例化
                return (T) implementationClass.getDeclaredConstructor().newInstance();
            } catch (Exception ex) {
                logger.error("创建服务实例失败: {}", implementationClass.getName(), ex);
                return null;
            }
        }
    }
    
    /**
     * 提取服务实现信息
     */
    private ServiceImplementationInfo extractImplementationInfo(Class<?> implementationClass) {
        try {
            Annotation[] annotations = implementationClass.getAnnotations();
            
            for (Annotation annotation : annotations) {
                if (annotation instanceof LicenseImplementation) {
                    // 许可证实现，优先级最高
                    return new ServiceImplementationInfo(1, "LicenseImplementation", true);
                    
                } else if (annotation instanceof FallbackImplementation) {
                    // 回退实现，获取priority属性
                    FallbackImplementation fallback = (FallbackImplementation) annotation;
                    return new ServiceImplementationInfo(fallback.priority(), "FallbackImplementation", false);
                }
            }
            
            // 如果没有找到特定注解，使用默认优先级
            return new ServiceImplementationInfo(999, "Default", false);
            
        } catch (Exception e) {
            logger.debug("提取服务实现信息失败: {} - {}", implementationClass.getName(), e.getMessage());
            return new ServiceImplementationInfo(999, "Default", false);
        }
    }
    
    /**
     * 判断是否为更好的候选实现
     */
    private boolean isBetterCandidate(ServiceImplementationInfo info, int currentBestPriority, boolean currentBestIsPremium) {
        // 许可证实现优先级最高
        if (info.isPremium && !currentBestIsPremium) {
            return true;
        }
        if (!info.isPremium && currentBestIsPremium) {
            return false;
        }
        
        // 相同类型比较数字优先级（数字越小优先级越高）
        return info.priority < currentBestPriority;
    }
    
    /**
     * 委托其他方法到原始DI容器
     */
    public <T> void registerSingleton(Class<T> serviceClass, T instance) {
        delegateInjector.registerSingleton(serviceClass, instance);
    }
    
    public <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass) {
        delegateInjector.registerImplementation(serviceClass, implementationClass);
    }
    
    /**
     * 服务实现信息
     */
    private static class ServiceImplementationInfo {
        final int priority;
        final String annotationType;
        final boolean isPremium;
        
        ServiceImplementationInfo(int priority, String annotationType, boolean isPremium) {
            this.priority = priority;
            this.annotationType = annotationType;
            this.isPremium = isPremium;
        }
        
        @Override
        public String toString() {
            return String.format("ServiceImplementationInfo{priority=%d, type=%s, premium=%s}", 
                               priority, annotationType, isPremium);
        }
    }
}
