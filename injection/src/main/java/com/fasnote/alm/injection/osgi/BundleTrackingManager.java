package com.fasnote.alm.injection.osgi;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.BundleEvent;
import org.osgi.framework.BundleListener;
import org.osgi.util.tracker.ServiceTracker;
import org.osgi.util.tracker.ServiceTrackerCustomizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * Bundle 跟踪管理器实现
 * 
 * 基于 OSGi BundleListener 实现事件驱动的 Bundle 跟踪机制
 * 当 Bundle 状态变化时，自动触发相应的扫描操作
 */
public class BundleTrackingManager implements IBundleTrackingManager, BundleListener {
    
    private static final Logger logger = LoggerFactory.getLogger(BundleTrackingManager.class);
    
    private BundleContext bundleContext;
    private BundleEventCallback callback;
    private volatile boolean tracking = false;
    private final Object trackingLock = new Object();

    // 跟踪的 Bundle 集合
    private final Set<String> trackedBundles = ConcurrentHashMap.newKeySet();

    // 包扫描提供者跟踪
    private ServiceTracker<Object, Object> packageScanProviderTracker;
    private final Map<String, String[]> bundlePackageMappings = new ConcurrentHashMap<>();
    
    @Override
    public void start(BundleContext bundleContext) {
        if (bundleContext == null) {
            throw new IllegalArgumentException("BundleContext 不能为空");
        }
        
        synchronized (trackingLock) {
            if (tracking) {
                logger.debug("Bundle 跟踪已经启动");
                return;
            }
            
            this.bundleContext = bundleContext;
            
            logger.info("启动 Bundle 跟踪管理器");
            
            try {
                // 注册 Bundle 监听器
                bundleContext.addBundleListener(this);

                // 启动包扫描提供者跟踪器
                startPackageScanProviderTracking();

                // 扫描当前已存在的 Bundle
                scanExistingBundles();

                tracking = true;

                logger.info("Bundle 跟踪管理器启动成功，当前跟踪 {} 个 Bundle", trackedBundles.size());

            } catch (Exception e) {
                logger.error("启动 Bundle 跟踪管理器失败", e);
                throw new RuntimeException("启动 Bundle 跟踪管理器失败", e);
            }
        }
    }
    
    @Override
    public void stop() {
        synchronized (trackingLock) {
            if (!tracking) {
                logger.debug("Bundle 跟踪未启动");
                return;
            }
            
            logger.info("停止 Bundle 跟踪管理器");
            
            try {
                if (bundleContext != null) {
                    bundleContext.removeBundleListener(this);
                }

                // 停止包扫描提供者跟踪器
                stopPackageScanProviderTracking();

                tracking = false;
                trackedBundles.clear();
                bundlePackageMappings.clear();
                bundleContext = null;

                logger.info("Bundle 跟踪管理器已停止");

            } catch (Exception e) {
                logger.error("停止 Bundle 跟踪管理器失败", e);
            }
        }
    }
    
    @Override
    public boolean isTracking() {
        return tracking;
    }
    
    @Override
    public Set<String> getTrackedBundles() {
        return Set.copyOf(trackedBundles);
    }
    
    @Override
    public void triggerBundleScan(String bundleSymbolicName) {
        if (bundleSymbolicName == null || bundleSymbolicName.trim().isEmpty()) {
            logger.warn("Bundle 符号名称为空，无法触发扫描");
            return;
        }
        
        if (!tracking || bundleContext == null) {
            logger.warn("Bundle 跟踪未启动，无法触发扫描");
            return;
        }
        
        // 查找指定的 Bundle
        Bundle targetBundle = null;
        for (Bundle bundle : bundleContext.getBundles()) {
            if (bundleSymbolicName.equals(bundle.getSymbolicName())) {
                targetBundle = bundle;
                break;
            }
        }
        
        if (targetBundle == null) {
            logger.warn("未找到 Bundle: {}", bundleSymbolicName);
            return;
        }
        
        logger.info("手动触发 Bundle 扫描: {}", bundleSymbolicName);
        
        // 移除跟踪记录，强制重新扫描
        trackedBundles.remove(bundleSymbolicName);
        
        // 触发扫描
        handleBundleReady(targetBundle);
    }
    
    @Override
    public void setBundleEventCallback(BundleEventCallback callback) {
        this.callback = callback;
    }
    
    @Override
    public void bundleChanged(BundleEvent event) {
        Bundle bundle = event.getBundle();
        int eventType = event.getType();
        
        logger.debug("Bundle 事件: {} - {} (状态: {})", 
                    getEventTypeName(eventType), 
                    bundle.getSymbolicName(), 
                    bundle.getState());
        
        switch (eventType) {
            case BundleEvent.RESOLVED:
            case BundleEvent.STARTING:
            case BundleEvent.STARTED:
                handleBundleReady(bundle);
                break;
                
            case BundleEvent.STOPPING:
            case BundleEvent.STOPPED:
            case BundleEvent.UNINSTALLED:
                handleBundleRemoved(bundle);
                break;
                
            default:
                // 忽略其他事件类型
                break;
        }
    }
    
    /**
     * 扫描当前已存在的 Bundle
     */
    private void scanExistingBundles() {
        if (bundleContext == null) {
            return;
        }
        
        logger.info("扫描当前已存在的 Bundle");
        
        Bundle[] bundles = bundleContext.getBundles();
        for (Bundle bundle : bundles) {
            if (isBundleReady(bundle)) {
                handleBundleReady(bundle);
            }
        }
    }
    
    /**
     * 处理 Bundle 就绪事件
     */
    private void handleBundleReady(Bundle bundle) {
        if (bundle == null) {
            return;
        }
        
        String bundleSymbolicName = bundle.getSymbolicName();
        
        // 检查 Bundle 状态是否适合扫描
        if (!isBundleReady(bundle)) {
            logger.debug("Bundle 状态不适合扫描: {} (状态: {})", bundleSymbolicName, bundle.getState());
            return;
        }
        
        // 检查是否已经处理过
        if (trackedBundles.contains(bundleSymbolicName)) {
            logger.debug("Bundle 已处理，跳过: {}", bundleSymbolicName);
            return;
        }
        
        logger.info("处理 Bundle 就绪事件: {} (状态: {})", bundleSymbolicName, bundle.getState());
        
        // 标记为已跟踪
        trackedBundles.add(bundleSymbolicName);
        
        // 获取Bundle的扫描包路径
        String[] scanPackages = getBundleScanPackages(bundle);

        // 异步通知回调，避免阻塞 Bundle 启动过程
        if (callback != null) {
            CompletableFuture.runAsync(() -> {
                try {
                    callback.onBundleReady(bundle, scanPackages);
                } catch (Exception e) {
                    logger.error("Bundle 就绪回调执行失败: {}", bundleSymbolicName, e);
                }
            });
        }
    }
    
    /**
     * 处理 Bundle 移除事件
     */
    private void handleBundleRemoved(Bundle bundle) {
        if (bundle == null) {
            return;
        }
        
        String bundleSymbolicName = bundle.getSymbolicName();
        
        logger.info("处理 Bundle 移除事件: {} (状态: {})", bundleSymbolicName, bundle.getState());
        
        // 移除跟踪记录
        trackedBundles.remove(bundleSymbolicName);
        
        // 通知回调
        if (callback != null) {
            try {
                callback.onBundleRemoved(bundle);
            } catch (Exception e) {
                logger.error("Bundle 移除回调执行失败: {}", bundleSymbolicName, e);
            }
        }
    }
    
    /**
     * 检查 Bundle 是否处于可扫描状态
     */
    private boolean isBundleReady(Bundle bundle) {
        int state = bundle.getState();
        return state == Bundle.RESOLVED || state == Bundle.STARTING || state == Bundle.ACTIVE;
    }
    
    /**
     * 获取事件类型名称（用于日志）
     */
    private String getEventTypeName(int eventType) {
        switch (eventType) {
            case BundleEvent.INSTALLED: return "INSTALLED";
            case BundleEvent.RESOLVED: return "RESOLVED";
            case BundleEvent.STARTING: return "STARTING";
            case BundleEvent.STARTED: return "STARTED";
            case BundleEvent.STOPPING: return "STOPPING";
            case BundleEvent.STOPPED: return "STOPPED";
            case BundleEvent.UPDATED: return "UPDATED";
            case BundleEvent.UNRESOLVED: return "UNRESOLVED";
            case BundleEvent.UNINSTALLED: return "UNINSTALLED";
            default: return "UNKNOWN(" + eventType + ")";
        }
    }

    /**
     * 启动包扫描提供者跟踪
     */
    private void startPackageScanProviderTracking() {
        try {
            // 创建包扫描提供者服务跟踪器
            packageScanProviderTracker = new ServiceTracker<Object, Object>(
                bundleContext,
                "com.fasnote.alm.injection.api.IPackageScanProvider",
                new PackageScanProviderTrackerCustomizer()
            );

            packageScanProviderTracker.open();
            logger.info("包扫描提供者跟踪器已启动");

        } catch (Exception e) {
            logger.error("启动包扫描提供者跟踪器失败", e);
        }
    }

    /**
     * 停止包扫描提供者跟踪
     */
    private void stopPackageScanProviderTracking() {
        try {
            if (packageScanProviderTracker != null) {
                packageScanProviderTracker.close();
                packageScanProviderTracker = null;
            }
            logger.info("包扫描提供者跟踪器已停止");

        } catch (Exception e) {
            logger.error("停止包扫描提供者跟踪器失败", e);
        }
    }

    /**
     * 获取Bundle的扫描包路径
     * 只返回Bundle明确声明的扫描包路径，如果Bundle没有注册IPackageScanProvider则返回空数组
     */
    private String[] getBundleScanPackages(Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();

        // 从包扫描提供者映射中查找
        String[] packages = bundlePackageMappings.get(bundleSymbolicName);
        if (packages != null && packages.length > 0) {
            logger.debug("从包扫描提供者获取Bundle扫描路径: {} -> {}", bundleSymbolicName, Arrays.toString(packages));
            return packages;
        }

        // 如果Bundle没有注册IPackageScanProvider或返回空包路径，返回空数组
        // 这表明该Bundle不希望其类被自动扫描和注册到DI容器中
        logger.debug("Bundle {} 未注册包扫描提供者，跳过DI扫描", bundleSymbolicName);
        return new String[0];
    }

    /**
     * 包扫描提供者服务跟踪器自定义器
     */
    private class PackageScanProviderTrackerCustomizer implements ServiceTrackerCustomizer<Object, Object> {

        @Override
        public Object addingService(org.osgi.framework.ServiceReference<Object> reference) {
            Object provider = bundleContext.getService(reference);
            if (provider != null) {
                addPackageScanProvider(provider, reference.getBundle());
            }
            return provider;
        }

        @Override
        public void modifiedService(org.osgi.framework.ServiceReference<Object> reference, Object provider) {
            if (provider != null) {
                removePackageScanProvider(provider, reference.getBundle());
                addPackageScanProvider(provider, reference.getBundle());
            }
        }

        @Override
        public void removedService(org.osgi.framework.ServiceReference<Object> reference, Object provider) {
            if (provider != null) {
                removePackageScanProvider(provider, reference.getBundle());
            }
            bundleContext.ungetService(reference);
        }
    }

    /**
     * 添加包扫描提供者
     */
    private void addPackageScanProvider(Object provider, Bundle bundle) {
        try {
            // 使用反射调用IPackageScanProvider的方法
            String[] scanPackages = (String[]) provider.getClass().getMethod("getScanPackages").invoke(provider);
            String bundleSymbolicName = bundle.getSymbolicName();

            if (scanPackages != null && scanPackages.length > 0) {
                bundlePackageMappings.put(bundleSymbolicName, scanPackages);
                logger.info("Bundle {} 注册DI扫描包路径: {}", bundleSymbolicName, Arrays.toString(scanPackages));

                // 如果Bundle已经被跟踪，触发重新扫描
                if (trackedBundles.contains(bundleSymbolicName)) {
                    logger.info("Bundle {} 已跟踪，触发重新扫描", bundleSymbolicName);
                    handleBundleReady(bundle);
                }
            } else {
                logger.debug("Bundle {} 的包扫描提供者返回空包路径，该Bundle不参与DI扫描", bundleSymbolicName);
            }

        } catch (Exception e) {
            logger.error("添加包扫描提供者失败: {}", bundle.getSymbolicName(), e);
        }
    }

    /**
     * 移除包扫描提供者
     */
    private void removePackageScanProvider(Object provider, Bundle bundle) {
        String bundleSymbolicName = bundle.getSymbolicName();
        String[] removedPackages = bundlePackageMappings.remove(bundleSymbolicName);

        if (removedPackages != null) {
            logger.info("Bundle {} 注销DI扫描包路径: {}", bundleSymbolicName, Arrays.toString(removedPackages));
        } else {
            logger.debug("Bundle {} 未注册DI扫描包路径，无需移除", bundleSymbolicName);
        }
    }
}
